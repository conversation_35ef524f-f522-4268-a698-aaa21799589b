# see https://docs.gitlab.com/ee/ci/yaml/README.html for all available options
variables:
  KUBE_NAMESPACE: "$CI_PROJECT_ROOT_NAMESPACE-test"
  KUBE_DEPLOYMENT_NAME: $CI_PROJECT_NAME
  DOCKER_REPO: "$DOCKER_REGISTRY/hzwangda/$CI_PROJECT_ROOT_NAMESPACE/$CI_PROJECT_NAME:build-$CI_PIPELINE_ID"
  # 👇 强制禁用 pipefail 注入
  FF_SKIP_WIN_CMD_EXEC: "true"
  FF_CMD_DISABLE_DELAYED_ERROR_LEVEL_EXPANSION: "true"
  FF_DISABLE_UMASK_FOR_DOCKER_EXECUTOR: "true"
  FF_SKIP_SCRIPT_PATH_CHECK: "true"
  # 设置为 bash 来避免 dash 的兼容性问题
  SHELL: "/bin/bash"
  # 启用 feature flags 来禁用自动注入 set -o pipefail
  FF_SKIP_PIPE_FAIL: "true"

before_script:
  # 确保使用的是 bash
  - exec bash

workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "web"
      when: always
    - if: $CI_COMMIT_REF_NAME == "master"
      when: never
    - if: $CI_COMMIT_REF_NAME == "dev"
      when: never
    - if: $CI_COMMIT_REF_NAME =~ /^prev/
      when: always
    - if: $CI_COMMIT_REF_NAME =~ /^test/
      when: always
    - when: never

stages:
  - build-vue
  - build-docker
  - deploy-rancher

build_vue:
  stage: build-vue
  image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/node:22.17.0-bullseye-slim
  script:
    - |
      #!/bin/bash
      set -e
      echo "🔧 构建 Vue 应用..."
      echo "当前 shell: $SHELL"
      npm config set registry https://mvnrepository.hzwangda.com/repository/npm/
      npm install
      npm run build
  cache:
    key: node-modules-cache
    paths:
      - node_modules/
  artifacts:
    paths:
      - dist/

bulid_image:
  stage: build-docker
  variables:
    DOCKER_HOST: tcp://localhost:2375
    DOCKER_TLS_CERTDIR: ''
  image: $DOCKER_REGISTRY_PUBLIC/docker:stable
  services:
    - name: $DOCKER_REGISTRY_PUBLIC/docker:18.09-dind
      command: ["--registry-mirror=https://$DOCKER_REGISTRY_PUBLIC"]
  script:
    - docker login -u $DOCKER_USER -p $DOCKER_PASSWORD $DOCKER_REGISTRY
    - docker build -t $DOCKER_REPO .
    - docker push $DOCKER_REPO
  cache:

deploy_rancher:
  stage: deploy-rancher
  variables:
    GIT_STRATEGY: none # 无需拉git源码
  image: $DOCKER_REGISTRY_PUBLIC/hzwangda/rancher-cli-k8s:2.4.10
  script:
    - rancher login $RACHER_SERVER --token $RACHER_TOKEN --context $RACHER_CONTEXT
    - rancher kubectl set image deployment/$KUBE_DEPLOYMENT_NAME $KUBE_DEPLOYMENT_NAME=$DOCKER_REPO --namespace=$KUBE_NAMESPACE
  after_script:
    - echo "rancher kubectl set image deployment/$KUBE_DEPLOYMENT_NAME $KUBE_DEPLOYMENT_NAME=$DOCKER_REPO --namespace=$KUBE_NAMESPACE"
  cache:
  dependencies: [] # 不需要前面环节的产物artifacts
