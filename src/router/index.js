import Home from '../views/Home.vue'
import Reservation from '../views/Reservation.vue'
import MyReservations from '../views/MyReservations.vue'
import CheckIn from '../views/CheckIn.vue'
import Admin from '../views/Admin.vue'
import NoShowManagement from '../views/NoShowManagement.vue'
import NoShowUserManagement from '../views/NoShowUserManagement.vue'
import DriverManagement from '../views/DriverManagement.vue'
import VacationManagement from '../views/VacationManagement.vue'
import VehicleDriverAssignment from '../views/ShiftAssignment.vue'
import VehicleDriverAssignmentWithWeekView from '../views/VehicleDriverAssignmentWithWeekView.vue'
import Page401 from '../views/401.vue'
import { setToken, getToken } from '../utils/auth'
import { dingTalk } from '../utils/dingtalk'
import { useBusStore } from '../stores/bus'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: '校车预约',
    }
  },
  {
    path: '/401',
    name: 'Page401',
    component: Page401,
    meta: {
      title: '未授权访问',
    }
  },
  {
    path: '/reservation',
    name: 'Reservation',
    component: Reservation,
    meta: {
      title: '预约班车',
    }
  },
  {
    path: '/my-reservations',
    name: 'MyReservations',
    component: MyReservations,
    meta: {
      title: '我的预约',
    }
  },
  {
    path: '/checkin',
    name: 'CheckIn',
    component: CheckIn,
    meta: {
      title: '签到确认',
    }
  },
  {
    path: '/admin',
    name: 'Admin',
    component: Admin,
    meta: {
      title: '管理后台',
    }
  },
  {
    path: '/noshow-management',
    name: 'NoShowManagement',
    component: NoShowManagement,
    meta: {
      title: '爽约管理',
    }
  },
  {
    path: '/noshow-user-management',
    name: 'NoShowUserManagement',
    component: NoShowUserManagement,
    meta: {
      title: '爽约用户管理',
    }
  },
  {
    path: '/driver-management',
    name: 'DriverManagement',
    component: DriverManagement,
    meta: {
      title: '驾驶员管理',
    }
  },
  {
    path: '/vacation-management',
    name: 'VacationManagement',
    component: VacationManagement,
    meta: {
      title: '假期日期管理',
    }
  },
  {
    path: '/vehicle-driver-assignment',
    name: 'VehicleDriverAssignment',
    component: VehicleDriverAssignment,
    meta: {
      title: '班次安排',
    }
  },
  {
    path: '/vehicle-driver-assignment-weekview',
    name: 'VehicleDriverAssignmentWithWeekView',
    component: VehicleDriverAssignmentWithWeekView,
    meta: {
      title: '车辆驾驶员管理（周视图）',
    }
  }
]

const whiteList = [ "/401"]; // no redirect whitelist
// 添加路由守卫函数
export function setupRouterGuard(router) {

  // 全局前置守卫
  router.beforeEach(async (to, from, next) => {
    // 设置页面标题
    document.title = to.meta?.title || '校车预约'
    const busStore = useBusStore()

    if (import.meta.env.VITE_APP_ENV==="development") {
        const data = {"user": {"roles": ["admin"], "user": {"createBy": "System", "createTime": "2025-09-11 14:05:03", "dept": {"id": 25822, "name": "默认部门"}, "enabled": true, "id": 2945232136896512, "jobs": [], "nickName": "wangzy", "phone": "18300wangzy", "roles": [{"dataScope": "全部", "id": 2, "level": 2, "name": "教师"},{"dataScope": "全部","id": 2943816427110400,"level": 3,"name": "校车管理员"}], "updateBy": "System", "updateTime": "2025-09-11 14:05:03", "username": "wangzy"}},"token": "Bearer eyJhbGciOiJIUzUxMiJ9.eyJqdGkiOiJhNzI0MzQzMzM5YjI0MzAxYjIzNjBkYmU1MTcxY2M3MiIsImF1dGgiOiJhZG1pbiIsInN1YiI6ImFkbWluIn0.ND-SM6I4uudf1xOMSTe3K-64my57frYH4XagTDXiG39lZ28tV7W7iijDCjaDiMyNyd-UVCDGMFqN4hFkLMqRXQ", "role": [{"dataScope": "全部", "id": 2, "level": 2, "name": "教师"}]}
        setToken(data.token)
        const busStore = useBusStore()
        busStore.userInfo = data.user
        busStore.role = data.role
        next()
      }

    const token = getToken()
    if (token && busStore.userInfo && busStore.userInfo.user && busStore.userInfo.user.nickName) {
      next()
    } else if (whiteList.indexOf(to.path) !== -1) {
      next() 
    } else {
      // 没有token或用户信息不完整，尝试钉钉自动登录
      const loginSuccess = await dingTalk.autoLogin()
      if (loginSuccess) {
        // 登录成功后重新执行路由守卫
        next({ ...to, replace: true })
      } else {
        // 登录失败，重定向到401页面
        next({ path: '/401', replace: true })
      }
    }
  })
}

export default routes