<template>
  <div class="vehicle-driver-assignment-page">
    <div class="page-container">
      <!-- 日期选择 -->
      <div class="date-selector card">
        <van-field
          v-model="selectedDateText"
          name="date"
          label="选择日期"
          placeholder="请选择日期"
          readonly
          is-link
          @click="showDatePicker = true"
        />
      </div>
      
      <!-- 班车列表 -->
      <div class="assignment-content card">
        <div v-if="loading" class="loading-container">
          <van-loading type="spinner" color="#1989fa" />
          <span>加载中...</span>
        </div>
        
        <div v-else-if="dateSchedules.length > 0" class="schedule-list">
          <div 
            v-for="schedule in dateSchedules" 
            :key="schedule.id"
            class="schedule-item"
          >
            <div class="schedule-header">
              <div class="schedule-info">
                <div class="schedule-time">{{ schedule.busTime }}</div>
                <div class="schedule-route">{{ schedule.start_station }} → {{ schedule.end_station }}</div>
                <div class="schedule-passenger-count">已预约: {{ schedule.peopleNum || 0 }}人</div>
              </div>
              <div class="schedule-stations">
                <template v-for="(station, index) in schedule.station.split(',')" :key="index">
                <van-tag 
                  size="mini"
                  type="primary"
                  plain
                >
                  {{ station }}
                </van-tag>
              </template>
              </div>
            </div>
            
            <div class="assignment-form">
              <van-cell-group>
                <van-field
                  v-model="schedule.licensePlate"
                  name="licensePlate"
                  label="车牌号"
                  placeholder="请输入或选择车牌号"
                  label-width="42px"
                >
                  <template #right-icon>
                    <van-button 
                      size="mini" 
                      type="primary" 
                      @click.stop="showBusSelector = true; selectedScheduleForBus = schedule"
                      style="margin-left: 8px; padding: 4px 12px; font-size: 12px;"
                    >
                      选择
                    </van-button>
                  </template>
                </van-field>
                <van-field
                  v-model="schedule.driverName"
                  name="driverName"
                  label="驾驶员"
                  placeholder="请选择驾驶员"
                  readonly
                  is-link
                  label-width="42px"
                  @click="showDriverSelector = true; selectedScheduleForDriver = schedule"
                />
              </van-cell-group>
              
              <div class="action-buttons">
                <van-button size="small" type="primary" @click="saveAssignment(schedule)">
                  保存安排
                </van-button>
              </div>
            </div>
          </div>
        </div>
        
        <van-empty v-else description="该日期暂无班次" />
      </div>
      
      <!-- 底部消息推送按钮 -->
      <div class="bottom-push-button">
        <van-button 
          type="primary" 
          @click="pushInformation"
          :loading="pushing"
          loading-text="消息推送中..."
          block
        >
          推送车辆信息
        </van-button>
      </div>
    </div>
    
    <!-- 日期选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-date-picker
        v-model="selectedDate"
        title="选择日期"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>
    
    <!-- 车辆选择器 -->
    <van-popup v-model:show="showBusSelector" position="bottom" :style="{ height: '70%' }">
      <div class="selector-popup">
        <div class="popup-header">
          <h3>选择车辆</h3>
          <van-icon name="cross" @click="showBusSelector = false" />
        </div>
        
        <div class="selector-list">
          <div 
            v-for="vehicle in bus"
            :key="vehicle.id"
            class="selector-item"
            :class="{ 'selected': selectedScheduleForBus?.licensePlate === vehicle.licensePlate }"
            @click="selectBus(vehicle)"
          >
            <div class="selector-item-info">
              <div class="selector-item-title">{{ vehicle.licensePlate }}</div>
              <div class="selector-item-desc">座位数: {{ vehicle.seat }}座 (可用: {{ vehicle.availableSeat }}座)</div>
            </div>
            <van-icon name="success" v-if="selectedScheduleForBus?.licensePlate === vehicle.licensePlate" class="selected-icon" />
          </div>
          <van-empty 
            v-if="bus.length === 0"
            description="暂无车辆数据"
          />
        </div>
      </div>
    </van-popup>
    
    <!-- 驾驶员选择器 -->
    <van-popup v-model:show="showDriverSelector" position="bottom" :style="{ height: '70%' }">
      <div class="selector-popup">
        <div class="popup-header">
          <h3>选择驾驶员</h3>
          <van-icon name="cross" @click="showDriverSelector = false" />
        </div>
        
        <div class="selector-list">
          <div 
            v-for="driver in driverUsers"
            :key="driver.id"
            class="selector-item"
            :class="{ 'selected': selectedScheduleForDriver?.driverId === driver.id }"
            @click="selectDriver(driver)"
          >
            <div class="selector-item-info">
              <div class="selector-item-title">{{ driver.name }}</div>
              <div class="selector-item-desc">{{ driver.phone }}</div>
            </div>
            <van-icon name="success" v-if="selectedScheduleForDriver?.driverId === driver.id" class="selected-icon" />
          </div>
          <van-empty 
            v-if="driverUsers.length === 0"
            description="暂无驾驶员数据"
          />
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { showToast, showDialog } from 'vant'
import dayjs from 'dayjs'
import { schedulesApi } from '../api/schedules.js'
import { driversApi } from '../api/drivers.js'
import { busmanagementApi } from '../api/busmanagement.js'
import { reservationApi } from '../api/reservation.js'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const selectedDateText = ref('')
const selectedDate = ref([])
const dateSchedules = ref([])

// 驾驶员数据
const driverUsers = ref([])

// 汽车数据
const bus = ref([])


// 获取驾驶员列表
const loadAvailableDrivers = async () => {
  try {
    // 调用API获取驾驶员
    const drivers = await driversApi.list()
    // 驾驶员接口获取到的数据格式 [
    //   {
    //     "id": 1,
    //     "name": "施卫群",
    //     "phone": "13858039077"
    //   }
    // ] 
    driverUsers.value = drivers
  } catch (error) {
    console.error('获取驾驶员列表失败:', error)
    // 出错时使用默认驾驶员数据
  }
}
// 获取汽车列表
const loadBusmanagementApi = async () => {
  
  try {
    // 调用API获取汽车车辆
    const res = await busmanagementApi.list()
    //汽车车辆接口获取到的数据格式 [
    //   {
    //     "availableSeat": "4",
    //     "id": 1,
    //     "licensePlate": "浙00355警",
    //     "seat": "5",
    //     "type": "小型车"
    //   }
    // ] 
    bus.value = res
  } catch (error) {
    console.error('获取汽车列表失败:', error)
  }
}

// 弹窗控制
const showDatePicker = ref(false)
const showDriverSelector = ref(false)
const pushing = ref(false)

// 选择的对象
const selectedScheduleForDriver = ref(null)
const selectedScheduleForBus = ref(null)

// 车辆选择弹窗控制
const showBusSelector = ref(false)

// 初始化数据
onMounted(() => {
  // 设置默认日期为今天
  const now = new Date()
  selectedDate.value = [now.getFullYear(), now.getMonth() + 1, now.getDate()]
  selectedDateText.value = dayjs().format('YYYY年MM月DD日')
  
  loadDateSchedules()
  loadAvailableDrivers()
  loadBusmanagementApi()
})

// 加载指定日期的班次
const loadDateSchedules = async () => {
  if (!selectedDate.value || selectedDate.value.length === 0) {
    return
  }
  
  loading.value = true
  try {
    const dateStr = dayjs(selectedDate.value).format('YYYY-MM-DD')
    // 调用API获取班次数据
    const schedules = await schedulesApi.list({ busDate: dateStr })
    
    // 处理已保存的驾驶员数据，确保驾驶员信息正确回显
    const processedSchedules = schedules.map(schedule => {
      // 检查班次是否包含已保存的驾驶员信息
      if (schedule.drivers && schedule.drivers.id) {
        // 将驾驶员ID和姓名赋值给对应的字段，确保界面显示
        schedule.driverId = schedule.drivers.id
        schedule.driverName = schedule.drivers.name
      }
      return schedule
    })
    
    // 更新班次列表
    dateSchedules.value = processedSchedules
  } catch (error) {
    showToast('加载班次信息失败')
    console.error('加载班次信息失败:', error)
    // 出错时清空列表
    dateSchedules.value = []
  } finally {
    loading.value = false
  }
}

// 日期选择确认
const onDateConfirm = () => {
  selectedDateText.value = dayjs(selectedDate.value).format('YYYY年MM月DD日')
  showDatePicker.value = false
  loadDateSchedules()
}

// 选择驾驶员
const selectDriver = (driver) => {
  if (selectedScheduleForDriver.value) {
    selectedScheduleForDriver.value.driverId = driver.id
    selectedScheduleForDriver.value.driverName = driver.name
  }
  showDriverSelector.value = false
}

// 选择车辆
const selectBus = (vehicle) => {
  if (selectedScheduleForBus.value) {
    selectedScheduleForBus.value.licensePlate = vehicle.licensePlate
  }
  showBusSelector.value = false
}

// 推送车辆信息
const pushInformation = async () => {
  if (!selectedDate.value || selectedDate.value.length === 0) {
    showToast('请先选择日期')
    return
  }
   if (!dateSchedules.value || dateSchedules.value.length === 0) {
    showToast('当前选中日期没有配备班次。')
    return
  }
  
  // 直接使用页面上已有的班次数据进行校验，不需要重新加载
  pushing.value = true
  try {
    // 检查是否有空缺的班次
    const incompleteSchedules = dateSchedules.value.filter(schedule => {
      return !schedule.driverId || !schedule.licensePlate
    })
    
    if (incompleteSchedules.length > 0) {
      // 有班次未完成配置
      showDialog({
        title: '配置未完成',
        message: `有${incompleteSchedules.length}个班次未配置司机或车辆，请先完成配置再推送信息。`,
        confirmButtonText: '确定'
      })
      return
    }
    
    // 所有班次都已配置，可以推送信息
     const dateStr = dayjs(selectedDate.value).format('YYYY-MM-DD')
    await reservationApi.informationPush({ reservationDate: dateStr })
    showToast('消息推送成功')
  } catch (error) {
    showToast('消息推送失败')
    console.error('推送消息失败:', error)
  } finally {
    pushing.value = false
  }
}

// 保存班次安排
const saveAssignment = async (schedule) => {
  if (!schedule.licensePlate.trim()) {
    showToast('请输入车牌号')
    return
  }
  
  if (!schedule.driverId) {
    showToast('请选择驾驶员')
    return
  }
  
  submitting.value = true
  try {
    // 调用API保存班次安排
    await schedulesApi.arrangeDrivers({
      id: schedule.id, // 班次ID
      start_station: schedule.start_station, // 出发地
      end_station: schedule.end_station, // 目的地
      busTime: schedule.busTime, // 时间
      busDate: schedule.busDate, // 日期
      station: schedule.station, // 站点
      drivers: {
        id: schedule.driverId, // 驾驶员ID
        name: schedule.driverName, // 驾驶员姓名
        phone: schedule.drivers?.phone || '' // 驾驶员手机号
      },
      licensePlate: schedule.licensePlate // 车牌
    })
    
    showToast('班次安排保存成功')
    // 保存成功后，重新加载数据以更新视图
    loadDateSchedules()
  } catch (error) {
    showToast('班次安排保存失败')
    console.error('保存班次安排失败:', error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.vehicle-driver-assignment-page {
  width: 100%;
  min-height: 100vh;
  background-color: #f7f8fa;
  overflow-x: hidden;
}

.page-container {
  max-width: 1200px;
  margin: 0 auto;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 20px;
  gap: 8px;
}

.date-selector {
  margin-bottom: 16px;
}

.bottom-push-button {
  margin-top: 20px;
  margin-bottom: 30px;
  padding: 0 16px;
}

.assignment-content {
  margin-bottom: 16px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.loading-container span {
  margin-top: 12px;
  color: #646566;
}

.schedule-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.schedule-item {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.schedule-header {
  margin-bottom: 16px;
}

.schedule-info {
  margin-bottom: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.schedule-time {
  font-size: 18px;
  font-weight: 600;
  color: #1989fa;
}

.schedule-route {
  font-size: 14px;
  color: #303030;
}

.schedule-passenger-count {
  font-size: 14px;
  color: #909399;
}

.schedule-stations {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.assignment-form {
  background-color: #f7f8fa;
  border-radius: 8px;
  padding: 16px;
}

.action-buttons {
  margin-top: 16px;
}

.card {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

/* 弹窗样式 */
.selector-popup {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: white;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebedf0;
}

.popup-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #323233;
}

.selector-list {
  flex: 1;
  overflow-y: auto;
}

.selector-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #ebedf0;
}

.selector-item.selected {
  background-color: #e8f4ff;
}

.selector-item-info {
  flex: 1;
}

.selector-item-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 4px;
}

.selector-item-desc {
  font-size: 14px;
  color: #646566;
}

.selected-icon {
  color: #1989fa;
  font-size: 20px;
}
</style>