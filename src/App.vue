<script setup>
import { useRoute } from 'vue-router'
import { ref, watch, computed, onMounted } from 'vue'
import { useBusStore } from './stores/bus.js'

const route = useRoute()
const busStore = useBusStore()

// 底部导航栏当前选中项
const activeTab = ref(0)

// 计算是否显示底部导航栏（登录状态下显示）
const showTabbar = computed(() => {
  return busStore.isLoggedIn
})

// 监听路由变化更新标题和选中的标签
watch(route, (to) => {

  // 更新底部导航栏选中状态
  switch (to.path) {
    case '/':
      activeTab.value = 0
      break
    case '/my-reservations':
      activeTab.value = 1
      break
    case '/checkin':
      activeTab.value = 2
      break
    case '/admin':
      activeTab.value = 3
      break
    default:
      activeTab.value = 0
  }
}, { immediate: true })

// 标签页切换处理
const onTabChange = (index) => {
  activeTab.value = index
  // 标签页切换逻辑已由路由处理
}

// vconsole相关状态
const clickNum = ref(0)
const clickBeforeTime = ref(0)

// 触发显示/隐藏VConsole的函数
const showVconsole = () => {
  manyClick(4)
}

/**
 * 处理连续点击逻辑
 * @param {number} _clickNum - 目标点击次数
 */
const manyClick = (_clickNum) => {
  // 安全校验
  if (typeof _clickNum !== "number") {
    return
  }
  
  // 处理clickNum的新值情况
  if (clickNum.value === 0) {
    clickNum.value = _clickNum
  } else {
    if (clickNum.value < 1 || clickNum.value > 10) {
      clickNum.value = 1
    } /*只准1击至10击，其他情况默认1击*/
  }
  
  // 处理点击之时差
  let clickTime = Date.parse(new Date()) + new Date().getMilliseconds() // 毫秒时间戳
  if (clickTime - clickBeforeTime.value < 400) {
    // 下一次点击是否成功
    clickBeforeTime.value =
      Date.parse(new Date()) + new Date().getMilliseconds()
    clickNum.value--
  } else {
    // 第一次点击
    clickBeforeTime.value =
      Date.parse(new Date()) + new Date().getMilliseconds()
    if (clickNum.value < _clickNum) {
      /*清除历史不成功点击的参数*/
      clickNum.value = _clickNum
    }
  }
  
  // N次成功点击后切换vconsole显示状态，并初始化clickNum
  if (clickNum.value === 1) {
    try {
      const vconsoleElement = document.getElementById("__vconsole")
      if (vconsoleElement) {
        if (
          vconsoleElement.style.display === "" ||
          vconsoleElement.style.display === "none"
        ) {
          vconsoleElement.style.display = "inline"
        } else {
          vconsoleElement.style.display = "none"
        }
      }
    } catch (error) {
    }
    clickNum.value = 0 /*初始化点击次数*/
  }
}

// 组件挂载时初始化用户状态
onMounted(() => {
  busStore.initUserState()
})
</script>

<template>
    <div class="On_vconsole" @click="showVconsole"></div>
    <!-- 主要内容区域 -->
    <div class="main-content">
      <router-view />
    </div>

    <!-- 底部导航栏 -->
    <van-tabbar
      v-if="showTabbar"
      v-model="activeTab"
      @change="onTabChange"
      fixed
      placeholder
    >
      <van-tabbar-item icon="home-o" to="/">首页</van-tabbar-item>
      <van-tabbar-item icon="calendar-o" to="/my-reservations">我的预约</van-tabbar-item>
      <van-tabbar-item icon="checked" to="/checkin">签到</van-tabbar-item>
      <van-tabbar-item icon="setting-o" to="/admin" v-permission="['校车管理员','教师']">管理</van-tabbar-item>
    </van-tabbar>
</template>



<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: #f7f8fa;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-user-select: none;
  -webkit-tap-highlight-color: transparent;
}

#app {
  width: 100%;
  min-height: 100vh;
  padding: 0;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  position: relative;
  background-color: #f7f8fa;
}

.main-content {
  flex: 1;
  width: 100%;
  min-height: calc(100vh - 50px); /* 减去底部tabbar高度 */
  padding: 0;
  margin: 0;
  overflow-x: hidden;
  background-color: #f7f8fa;
}

/* 自定义样式 */
.page-container {
  width: 100%;
  padding: 12px;
  margin: 0;
  box-sizing: border-box;
  overflow-x: hidden;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 12px;
}

.card {
  width: 100%;
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  box-sizing: border-box;
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  html, body {
    width: 100%;
    overflow-x: hidden;
  }

  #app {
    width: 100%;
    overflow-x: hidden;
  }

  .main-content {
    width: 100%;
    overflow-x: hidden;
  }

  .page-container {
    width: 100%;
    padding: 8px;
    overflow-x: hidden;
  }

  .card {
    width: 100%;
    padding: 12px;
    margin-bottom: 8px;
    border-radius: 6px;
    overflow-x: hidden;
  }

  .section-title {
    font-size: 16px;
    margin-bottom: 8px;
  }
}

/* 超小屏幕适配 */
@media screen and (max-width: 375px) {
  .page-container {
    padding: 6px;
  }

  .card {
    padding: 10px;
    margin-bottom: 6px;
  }
}

/* 防止所有元素产生横向滚动 */
* {
  max-width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* 确保表单元素不超出容器 */
.van-field, .van-button, .van-cell {
  max-width: 100%;
  box-sizing: border-box;
}

/* 确保文本不会导致横向滚动 */
p, div, span, h1, h2, h3, h4, h5, h6 {
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
}

.On_vconsole {
  width: 70px;
  height: 20px;
  position: fixed;
  left: 50%;
  transform: translate(-50%);
  z-index: 99999;
}

#__vconsole{
  display: none;
}
</style>
