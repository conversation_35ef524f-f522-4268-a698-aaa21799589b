import { useBusStore } from '../../stores/bus.js'

// 权限检查函数
function checkPermission(el, binding) {
  const { value } = binding

  if (!value) {
    return
  }

  const busStore = useBusStore()

  // 从用户信息中获取角色
  const userRoles = busStore.role.map(item => item.name)

  // 将权限值转换为数组
  const requiredRoles = Array.isArray(value) ? value : [value]

  // 检查用户是否有所需的角色权限
  const hasPermission = requiredRoles.some(requiredRole => {
    return userRoles.some(userRole => {
      // 支持多种角色名称格式
      const roleName = userRole.name || userRole
      return roleName === requiredRole
    })
  })

  // 如果没有权限，隐藏元素
  if (!hasPermission) {
    el.style.display = 'none'
  } else {
    el.style.display = ''
  }
}

export default {
  install(app) {
    app.directive('permission', {
      mounted(el, binding) {
        checkPermission(el, binding)
      },
      updated(el, binding) {
        checkPermission(el, binding)
      }
    })
  }
}
